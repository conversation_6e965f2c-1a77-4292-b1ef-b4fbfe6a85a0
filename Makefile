INCLUDE_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))/.phcstack

include $(INCLUDE_DIR)/with-deploy.mk
include $(INCLUDE_DIR)/with-registry.mk
include $(INCLUDE_DIR)/base.mk

.PHONY: install
install: before-install pip-install ##@ Installation des dépendances (et lance le service)

.PHONY: before-install
before-install: purge create-volumes ## Common action to run before installing the app

.PHONY: pip-install
pip-install: ## install python requirements
	@$(DOCKER_COMPOSE) run --rm -i $(EXEC_CONTAINER_NAME) python -m venv /var/www/clippy/venv
	@$(DOCKER_COMPOSE) run --rm -i $(EXEC_CONTAINER_NAME) pip install -r requirements.txt

.PHONY: run
run: ##@ Ouvre une session CLI sur l'image, sans lancer le service
	@$(DOCKER_COMPOSE) run --rm -it $(EXEC_CONTAINER_NAME) bash

.PHONY: required-dependencies
required-dependencies:: ##@ Démarre les dépendances requises (mais ne les installent pas).
	@bash $(BASE_DIR)/.phcstack/check_dependency.sh Traefik traefik traefik
