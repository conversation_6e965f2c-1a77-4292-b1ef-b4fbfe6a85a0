#!/usr/bin/env python3
"""
Script de test pour vérifier le fonctionnement du service de contrôle.
"""

import asyncio
import httpx
import time
import os
from dotenv import load_dotenv

load_dotenv(".env.local")
load_dotenv(".env")

async def test_control_server():
    """Test du service de contrôle."""
    
    app_port = int(os.getenv("APP_PORT", 8800))
    base_url = f"http://localhost:{app_port}"
    
    print(f"Test du service de contrôle sur {base_url}")
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            # Test du health check
            print("\n1. Test du health check...")
            response = await client.get(f"{base_url}/health")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.json()}")
            
            # Test du statut de contrôle
            print("\n2. Test du statut de contrôle...")
            response = await client.get(f"{base_url}/control/status")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.json()}")
            
            # Test d'une route personnalisée
            print("\n3. Test d'une route personnalisée...")
            response = await client.get(f"{base_url}/custom/info")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.json()}")
            
            # Attendre un peu que Parlant démarre complètement
            print("\n4. Attente du démarrage complet de Parlant...")
            await asyncio.sleep(3)
            
            # Test d'une route forwardée (si Parlant est prêt)
            print("\n5. Test d'une route forwardée vers Parlant...")
            try:
                response = await client.get(f"{base_url}/api/health")
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    print("✅ Forwarding vers Parlant fonctionne!")
                else:
                    print(f"⚠️  Forwarding vers Parlant: status {response.status_code}")
            except Exception as e:
                print(f"❌ Erreur lors du forwarding: {e}")
            
            print("\n✅ Tests terminés!")
            
        except Exception as e:
            print(f"❌ Erreur lors des tests: {e}")

if __name__ == "__main__":
    print("Démarrage des tests...")
    print("Assurez-vous que le service de contrôle est démarré avec: python src/main.py")
    print("Attente de 5 secondes pour laisser le temps au service de démarrer...")
    time.sleep(5)
    
    asyncio.run(test_control_server())
