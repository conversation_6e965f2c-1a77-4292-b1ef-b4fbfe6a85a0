# Architecture du Service de Contrôle Clippy

## Vue d'ensemble

Cette nouvelle architecture sépare le service Parlant du service de contrôle pour permettre plus de flexibilité et de contrôle sur les routes et la logique métier.

## Architecture

```
┌─────────────────────────────────────────┐
│           Client / Browser              │
└─────────────────┬───────────────────────┘
                  │ HTTP Requests
                  │ Port APP_PORT (8800)
                  ▼
┌─────────────────────────────────────────┐
│        Service de Contrôle              │
│        (FastAPI)                        │
│                                         │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Routes    │  │     Routes      │   │
│  │Personnalisées│  │   Forwardées    │   │
│  │             │  │                 │   │
│  │ /health     │  │ /api/*          │   │
│  │ /control/*  │  │ /agents/*       │   │
│  │ /custom/*   │  │ /conversations/*│   │
│  └─────────────┘  └─────────┬───────┘   │
└─────────────────────────────┼───────────┘
                              │ HTTP Proxy
                              │ Port INTERNAL_PORT (8801)
                              ▼
┌─────────────────────────────────────────┐
│         Service Parlant                 │
│         (parlant.sdk)                   │
│                                         │
│  ┌─────────────────────────────────┐    │
│  │        Agent IA                 │    │
│  │    "Son-Vidéo IA"              │    │
│  └─────────────────────────────────┘    │
└─────────────────────────────────────────┘
```

## Composants

### 1. Service de Contrôle (`src/control_server.py`)
- **Port**: APP_PORT (8800)
- **Rôle**: Point d'entrée principal, gestion des routes personnalisées et proxy
- **Fonctionnalités**:
  - Routes de santé et de contrôle
  - Proxy intelligent vers Parlant
  - Logique métier personnalisée
  - Gestion du cycle de vie de Parlant

### 2. Service Parlant (`src/services/parlant_service.py`)
- **Port**: INTERNAL_PORT (8801)
- **Rôle**: Service IA en arrière-plan
- **Fonctionnalités**:
  - Agent conversationnel
  - API Parlant complète
  - Gestion automatique du cycle de vie

### 3. Service Proxy (`src/services/proxy_service.py`)
- **Rôle**: Forward les requêtes vers Parlant
- **Fonctionnalités**:
  - Proxy HTTP transparent
  - Gestion des erreurs
  - Health checks

## Configuration

### Variables d'environnement
```bash
APP_PORT=8800        # Port du service de contrôle
INTERNAL_PORT=8801   # Port du service Parlant
OPENAI_API_KEY=...   # Clé API OpenAI
```

### Routes configurables
Voir `src/config/routes_config.py` pour personnaliser :
- Routes forwardées vers Parlant
- Routes gérées par le service de contrôle
- Configuration des timeouts et headers

## Utilisation

### Démarrage
```bash
# Installation des dépendances
pip install -r requirements.txt

# Démarrage du service
python src/main.py
```

### Endpoints disponibles

#### Routes de contrôle
- `GET /health` - Santé globale du système
- `GET /control/status` - Statut détaillé des services
- `POST /control/restart-parlant` - Redémarrage de Parlant

#### Routes personnalisées (exemples)
- `GET /custom/info` - Informations personnalisées

#### Routes forwardées vers Parlant
- `GET|POST /api/*` - API Parlant
- `GET|POST /agents/*` - Gestion des agents
- `GET|POST /conversations/*` - Gestion des conversations

### Tests
```bash
# Lancer les tests (service doit être démarré)
python test_control_server.py
```

## Personnalisation

### Ajouter une route personnalisée
```python
@app.get("/custom/ma-route")
async def ma_route_personnalisee():
    # Votre logique ici
    return {"message": "Ma logique personnalisée"}
```

### Modifier les routes forwardées
Éditez `src/config/routes_config.py` pour changer les routes forwardées.

### Ajouter de la logique avant forwarding
```python
@app.api_route("/api/special/{path:path}", methods=["GET", "POST"])
async def special_api(request: Request, path: str):
    # Logique personnalisée avant forwarding
    if path == "sensitive":
        # Vérifications de sécurité
        pass
    
    # Forward vers Parlant
    return await proxy_service.forward_request(request, f"/api/special/{path}")
```

## Avantages

1. **Contrôle total**: Vous contrôlez quelles routes sont exposées
2. **Logique personnalisée**: Ajout facile de logique métier
3. **Monitoring**: Health checks et statuts détaillés
4. **Flexibilité**: Modification des routes sans redémarrer Parlant
5. **Sécurité**: Filtrage et validation des requêtes
6. **Évolutivité**: Architecture modulaire et extensible
