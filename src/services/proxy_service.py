import httpx
import logging
from fastapi import Request, Response, HTTPException
from typing import Dict, Any, Optional
import json

logger = logging.getLogger(__name__)


class ProxyService:
    """Service pour faire du proxy vers le service Parlant."""
    
    def __init__(self, parlant_base_url: str):
        self.parlant_base_url = parlant_base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def close(self):
        """Ferme le client HTTP."""
        await self.client.aclose()
        
    async def forward_request(
        self, 
        request: Request, 
        path: str,
        method: str = None
    ) -> Response:
        """
        Forward une requête vers le service Parlant.
        
        Args:
            request: La requête FastAPI originale
            path: Le chemin à appeler sur le service Parlant
            method: La méthode HTTP (si None, utilise celle de la requête)
        """
        method = method or request.method
        url = f"{self.parlant_base_url}{path}"
        
        # Préparer les headers (exclure les headers de connexion)
        headers = dict(request.headers)
        excluded_headers = {
            'host', 'connection', 'content-length', 
            'transfer-encoding', 'upgrade'
        }
        headers = {
            k: v for k, v in headers.items() 
            if k.lower() not in excluded_headers
        }
        
        # Préparer les paramètres de requête
        params = dict(request.query_params)
        
        try:
            # Lire le body de la requête
            body = await request.body()
            
            logger.info(f"Forwarding {method} {url}")
            
            # Faire la requête vers Parlant
            response = await self.client.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                content=body if body else None
            )
            
            # Préparer la réponse
            response_headers = dict(response.headers)
            # Exclure certains headers de la réponse
            excluded_response_headers = {
                'content-length', 'transfer-encoding', 'connection'
            }
            response_headers = {
                k: v for k, v in response_headers.items()
                if k.lower() not in excluded_response_headers
            }
            
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=response_headers
            )
            
        except httpx.RequestError as e:
            logger.error(f"Erreur lors du forward vers {url}: {e}")
            raise HTTPException(
                status_code=503, 
                detail=f"Service Parlant indisponible: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Erreur inattendue lors du forward: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Erreur interne: {str(e)}"
            )
            
    async def health_check(self) -> Dict[str, Any]:
        """Vérifie la santé du service Parlant."""
        try:
            response = await self.client.get(f"{self.parlant_base_url}/health")
            return {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "status_code": response.status_code,
                "parlant_url": self.parlant_base_url
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "parlant_url": self.parlant_base_url
            }
