import asyncio
import os
import parlant.sdk as p
from typing import Optional
import logging
import httpx

logger = logging.getLogger(__name__)


class ParlantService:
    """Service pour gérer le serveur Parlant en arrière-plan."""
    
    def __init__(self, port: int):
        self.port = port
        self.server: Optional[p.Server] = None
        self.agent = None
        self._server_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Démarre le serveur Parlant."""
        if self._server_task is not None:
            logger.warning("Le service Parlant est déjà démarré")
            return
            
        logger.info(f"Démarrage du service Parlant sur le port {self.port}")
        self._server_task = asyncio.create_task(self._run_server())

        # Attendre que le serveur soit vraiment prêt
        await self._wait_for_server_ready()
        
    async def stop(self):
        """Arrête le serveur Parlant."""
        if self._server_task is not None:
            logger.info("Arrêt du service Parlant")
            self._server_task.cancel()
            try:
                await self._server_task
            except asyncio.CancelledError:
                pass
            self._server_task = None
            
    async def _run_server(self):
        """Exécute le serveur Parlant avec serveur HTTP."""
        try:
            # Essayer différentes configurations pour démarrer le serveur HTTP
            logger.info(f"Tentative de démarrage Parlant sur port {self.port}")
            async with p.Server(port=self.port) as server:
                self.server = server
                agent = await server.create_agent(
                    name="Son-Vidéo IA",
                    description="Tu es un conseiller Son-Vidéo.",
                )
                self.agent = agent
                logger.info(f"Service Parlant démarré avec succès sur le port {self.port}")

                # Garder le serveur en vie
                #while True:
                #    await asyncio.sleep(1)

        except asyncio.CancelledError:
            logger.info("Service Parlant arrêté")
            raise
        except Exception as e:
            logger.error(f"Erreur dans le service Parlant: {e}")
            raise
            
    @property
    def is_running(self) -> bool:
        """Vérifie si le service est en cours d'exécution."""
        return self._server_task is not None and not self._server_task.done()
        
    def get_base_url(self) -> str:
        """Retourne l'URL de base du service Parlant."""
        return f"http://localhost:{self.port}"

    async def _wait_for_server_ready(self, max_attempts: int = 30, delay: float = 1.0):
        """Attend que le serveur Parlant soit prêt à accepter les connexions."""
        logger.info(f"Attente que le serveur Parlant soit prêt sur le port {self.port}")

        async with httpx.AsyncClient(timeout=5.0) as client:
            for attempt in range(max_attempts):
                try:
                    # Essayer de faire une requête simple au serveur
                    response = await client.get(f"http://localhost:{self.port}/agents")
                    if response.status_code in [200, 404]:  # 404 est OK, ça veut dire que le serveur répond
                        logger.info(f"Serveur Parlant prêt après {attempt + 1} tentatives")
                        return
                except (httpx.RequestError, httpx.ConnectError):
                    # Le serveur n'est pas encore prêt
                    pass

                if attempt < max_attempts - 1:
                    logger.debug(f"Tentative {attempt + 1}/{max_attempts} - serveur pas encore prêt, attente {delay}s")
                    await asyncio.sleep(delay)

            logger.warning(f"Le serveur Parlant n'est pas devenu prêt après {max_attempts} tentatives")
            # Ne pas lever d'exception, continuer quand même
