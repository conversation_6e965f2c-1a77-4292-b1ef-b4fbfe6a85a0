"""
Configuration des routes à forwarder vers Parlant.
Vous pouvez modifier cette configuration pour contrôler quelles routes
sont forwardées et lesquelles sont gérées par votre logique personnalisée.
"""

# Routes qui seront forwardées vers Parlant
FORWARDED_ROUTES = [
    "/api",
    "/agents", 
    "/conversations",
    "/docs",
    "/openapi.json",
    "/redoc"
]

# Routes qui seront gérées par le service de contrôle
CONTROL_ROUTES = [
    "/health",
    "/control",
    "/custom"
]

# Configuration des méthodes HTTP autorisées pour le forwarding
ALLOWED_METHODS = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]

# Configuration du timeout pour les requêtes vers Parlant (en secondes)
PARLANT_TIMEOUT = 30.0

# Configuration des headers à exclure lors du forwarding
EXCLUDED_REQUEST_HEADERS = {
    'host', 'connection', 'content-length', 
    'transfer-encoding', 'upgrade'
}

EXCLUDED_RESPONSE_HEADERS = {
    'content-length', 'transfer-encoding', 'connection'
}
