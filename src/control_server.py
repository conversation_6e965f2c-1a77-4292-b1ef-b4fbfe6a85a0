import asyncio
import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response, HTTPException
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

from services.parlant_service import ParlantService
from services.proxy_service import ProxyService

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Charger les variables d'environnement
load_dotenv(".env.local")
load_dotenv(".env")

# Services globaux
parlant_service: ParlantService = None
proxy_service: ProxyService = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application."""
    global parlant_service, proxy_service
    
    # Démarrage
    internal_port = int(os.getenv("INTERNAL_PORT", 8801))
    
    logger.info("Démarrage du service de contrôle")
    
    # Démarrer le service Parlant
    parlant_service = ParlantService(internal_port)
    await parlant_service.start()
    
    # Initialiser le service de proxy
    proxy_service = ProxyService(parlant_service.get_base_url())
    
    logger.info(f"Service de contrôle démarré. Parlant sur le port {internal_port}")
    
    yield
    
    # Arrêt
    logger.info("Arrêt du service de contrôle")
    if proxy_service:
        await proxy_service.close()
    if parlant_service:
        await parlant_service.stop()


# Créer l'application FastAPI
app = FastAPI(
    title="Clippy Control Server",
    description="Service de contrôle pour Clippy avec proxy vers Parlant",
    version="1.0.0",
    lifespan=lifespan
)


@app.get("/health")
async def health_check():
    """Point de santé du service de contrôle."""
    parlant_health = await proxy_service.health_check() if proxy_service else {"status": "not_initialized"}
    
    return {
        "status": "healthy",
        "service": "control_server",
        "parlant": parlant_health,
        "parlant_running": parlant_service.is_running if parlant_service else False
    }


@app.get("/control/status")
async def control_status():
    """Statut détaillé du service de contrôle."""
    return {
        "control_server": {
            "status": "running",
            "port": int(os.getenv("APP_PORT", 8800))
        },
        "parlant_service": {
            "status": "running" if parlant_service and parlant_service.is_running else "stopped",
            "port": int(os.getenv("INTERNAL_PORT", 8801)),
            "url": parlant_service.get_base_url() if parlant_service else None
        }
    }


@app.post("/control/restart-parlant")
async def restart_parlant():
    """Redémarre le service Parlant."""
    global parlant_service
    
    if not parlant_service:
        raise HTTPException(status_code=500, detail="Service Parlant non initialisé")
    
    logger.info("Redémarrage du service Parlant demandé")
    
    # Arrêter le service
    await parlant_service.stop()
    
    # Redémarrer le service
    await parlant_service.start()
    
    return {"message": "Service Parlant redémarré avec succès"}


# Routes de proxy vers Parlant
# Vous pouvez personnaliser ces routes selon vos besoins

@app.api_route("/api/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_api(request: Request, path: str):
    """Proxy toutes les routes /api/* vers Parlant."""
    if not proxy_service:
        raise HTTPException(status_code=503, detail="Service de proxy non disponible")
    
    return await proxy_service.forward_request(request, f"/api/{path}")


@app.api_route("/agents/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_agents(request: Request, path: str):
    """Proxy toutes les routes /agents/* vers Parlant."""
    if not proxy_service:
        raise HTTPException(status_code=503, detail="Service de proxy non disponible")
    
    return await proxy_service.forward_request(request, f"/agents/{path}")


@app.api_route("/conversations/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_conversations(request: Request, path: str):
    """Proxy toutes les routes /conversations/* vers Parlant."""
    if not proxy_service:
        raise HTTPException(status_code=503, detail="Service de proxy non disponible")
    
    return await proxy_service.forward_request(request, f"/conversations/{path}")


# Route personnalisée - exemple d'ajout de logique
@app.get("/custom/info")
async def custom_info():
    """Route personnalisée avec logique métier."""
    # Ici vous pouvez ajouter votre logique personnalisée
    parlant_health = await proxy_service.health_check() if proxy_service else None
    
    return {
        "message": "Ceci est une route personnalisée",
        "timestamp": "2024-01-01T00:00:00Z",  # Vous pouvez utiliser datetime.now()
        "parlant_status": parlant_health,
        "custom_data": {
            "version": "1.0.0",
            "environment": "development"
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("APP_PORT", 8800))
    uvicorn.run(app, host="0.0.0.0", port=port)
