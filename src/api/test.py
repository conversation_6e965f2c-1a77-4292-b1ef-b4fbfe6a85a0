from typing import NewType, <PERSON><PERSON><PERSON><PERSON>, Annotated

from fastapi import APIRouter, Path
from parlant.api.common import ExampleJson, JSONSerializableDTO, apigen_config
from parlant.core.common import DefaultBaseModel
from starlette import status
from starlette.requests import Request

API_GROUP = "test"

test_example: ExampleJson = {
    "id": "test_123",
    "data": {
        "message": "Example message",
    },
}

TestId = NewType("TestId", str)


TestIdPath: TypeAlias = Annotated[
    TestId,
    Path(
        description="Unique identifier for the test object",
        examples=["test_123"],
    ),
]

class TestDTO(
    DefaultBaseModel,
    json_schema_extra={"example": test_example},
):
    """Represents a test response."""

    id: TestIdPath
    data: JSONSerializableDTO

def create_router()-> APIRouter:
    router = APIRouter()

    @router.get(
        "",
        operation_id="get_test",
        response_model=TestDTO,
        responses={
            status.HTTP_200_OK: {
                "description": """Get a test object.""",
                "content": {"application/json": {"example": [test_example]}},
            }
        },
        **apigen_config(group_name=API_GROUP, method_name="retrieve"),
    )
    async def retrieve_test(request: Request) -> TestDTO:
        """Returns a sample test object."""
        return TestDTO(
            id=TestId("toto_1"),
            data={"message": "Hello world"},
        )



    return router