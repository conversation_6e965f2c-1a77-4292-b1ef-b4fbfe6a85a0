import os
import uvicorn
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv(".env.local")
load_dotenv(".env")

if __name__ == "__main__":
    # Importer le serveur de contrôle
    from control_server import app
    
    # Lancer le serveur de contrôle sur APP_PORT
    port = int(os.getenv("APP_PORT", 80))
    print(f"Démarrage du service de contrôle sur le port {port}")
    print(f"Le service Parlant sera lancé sur le port {os.getenv('INTERNAL_PORT', 8801)}")
    
    uvicorn.run(app, host="0.0.0.0", port=port)
