import asyncio
import os
import parlant.sdk as p
from dotenv import load_dotenv

#debug
from pprint import pprint


load_dotenv(".env.local")
load_dotenv(".env")


async def main():
  async with p.Server(
          port=int(os.getenv("APP_PORT")),
          #configure_container=configure_container,
          #initialize_container=initialize_container,
  ) as server:
    agent = await server.create_agent(
        name="Son-Vidéo IA",
        description="Tu es un conseiller Son-Vidéo.",
    )
    pprint(vars(server))
    # log server object





asyncio.run(main())
